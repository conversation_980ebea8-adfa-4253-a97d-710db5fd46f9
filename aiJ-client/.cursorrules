{"ai": {"enabled": true, "ignore": ["node_modules/**", "dist/**", "build/**", "**/*.min.js", "**/*.bundle.js", "**/vendor/**", "**/third-party/**", "**/*.lock", "**/*.map", "**/*.meta", "**/*.bin", "**/*_atlas*.png", "library/**", "temp/**", "local/**"], "contextType": "auto", "contextDepth": 5}, "format": {"enabled": true, "formatOnSave": true, "formatOnPaste": true}, "linting": {"enabled": true, "lintOnSave": true}, "completion": {"enabled": true, "language": {"typescript": true, "javascript": true, "python": false, "java": true, "go": false, "rust": false, "c": false, "cpp": false, "csharp": false}}}