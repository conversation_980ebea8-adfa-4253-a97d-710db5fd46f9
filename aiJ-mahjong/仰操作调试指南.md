# 仰操作调试指南

## 问题诊断步骤

### 1. 检查服务端日志

启动游戏后，查看服务端控制台是否有以下日志：

```
🧪 测试模式：庄家将获得中发白三张牌，可以进行仰操作
🧪 庄家牌位置：XXX-XXX-XXX = 中发白
检查玩家0仰操作 - 中(XX):X, 发(XX):X, 白(XX):X
玩家0可以进行仰操作
检测到仰操作，暂停出牌流程，等待玩家操作
```

### 2. 检查客户端操作按钮

庄家发牌后，检查是否：
- "仰"操作按钮变红色高亮
- 其他玩家无法出牌

### 3. 点击"仰"操作后检查日志

点击"仰"操作按钮后，应该看到：

```
收到玩家0的仰操作请求
玩家0仰操作验证通过，移除中发白三张牌
玩家0完成仰操作
```

## 可能的问题和解决方案

### 问题1：没有检测到仰操作

**症状**：发牌后没有"仰"操作按钮
**原因**：庄家没有中发白三张牌
**解决**：检查测试数据配置

### 问题2：点击仰操作无反应

**症状**：点击"仰"按钮后没有任何变化
**可能原因**：
1. 客户端没有正确发送操作请求
2. 服务端没有正确处理操作请求
3. 操作验证失败

### 问题3：操作验证失败

**症状**：服务端日志显示"仰操作验证失败"
**原因**：玩家手牌中没有中发白三张牌
**解决**：检查测试数据和发牌逻辑

## 调试方法

### 1. 启用详细日志

在MahjongLogic.java的estimateY方法中添加更多日志：

```java
public Y estimateY(byte[] indices, int chair) {
    // 打印所有手牌
    System.out.println("玩家" + chair + "的手牌索引:");
    for (int i = 0; i < indices.length; i++) {
        if (indices[i] > 0) {
            System.out.println("  索引" + i + ": " + indices[i] + "张");
        }
    }
    
    // 检查中发白
    byte zhongIndex = MahjongKit.switchCardToIndex((byte)0x35);
    byte faIndex = MahjongKit.switchCardToIndex((byte)0x36);
    byte baiIndex = MahjongKit.switchCardToIndex((byte)0x37);
    
    System.out.println("中发白检查:");
    System.out.println("  中(0x35)索引" + zhongIndex + ": " + indices[zhongIndex] + "张");
    System.out.println("  发(0x36)索引" + faIndex + ": " + indices[faIndex] + "张");
    System.out.println("  白(0x37)索引" + baiIndex + ": " + indices[baiIndex] + "张");
    
    // 其余逻辑...
}
```

### 2. 检查测试数据

在onGameReset方法中添加日志，确认测试数据正确：

```java
// 打印测试数据的前几张牌
System.out.println("测试数据前10张牌:");
for (int i = 0; i < Math.min(10, repertoryCard.length); i++) {
    System.out.printf("  [%d]: 0x%02X\n", i, repertoryCard[i]);
}
```

### 3. 检查发牌结果

在dispatchCard方法中添加日志，确认庄家确实拿到了中发白：

```java
// 发牌后打印庄家手牌
if (chair == 0) { // 庄家
    System.out.println("庄家发牌后的手牌:");
    for (int i = 0; i < cardIndices[0].length; i++) {
        if (cardIndices[0][i] > 0) {
            byte card = MahjongKit.switchIndexToCard((byte)i);
            System.out.printf("  牌0x%02X: %d张\n", card, cardIndices[0][i]);
        }
    }
}
```

## 快速验证方法

### 方法1：手动设置庄家手牌

在dispatchCard方法的最后，手动给庄家添加中发白：

```java
if (chair == 0 && !gOpen) { // 庄家第一次发牌
    // 手动添加中发白
    cardIndices[0][MahjongKit.switchCardToIndex((byte)0x35)] = 1; // 中
    cardIndices[0][MahjongKit.switchCardToIndex((byte)0x36)] = 1; // 发
    cardIndices[0][MahjongKit.switchCardToIndex((byte)0x37)] = 1; // 白
    System.out.println("手动给庄家添加中发白三张牌");
}
```

### 方法2：简化测试数据

使用最简单的测试数据配置：

```java
repertoryCard = new byte[144];
// 前52张牌给4个玩家，每人13张
// 庄家的前3张牌设为中发白
repertoryCard[repertoryCard.length - 52] = 0x35; // 中
repertoryCard[repertoryCard.length - 51] = 0x36; // 发
repertoryCard[repertoryCard.length - 50] = 0x37; // 白
// 其余位置填充其他牌...
```

## 预期的完整流程

1. **游戏开始** → 发牌给4个玩家
2. **庄家发牌** → 检测到中发白三张牌
3. **发送仰操作通知** → 庄家客户端显示"仰"按钮
4. **暂停出牌流程** → 其他玩家无法出牌
5. **点击仰操作** → 发送操作请求到服务端
6. **服务端处理** → 验证并移除中发白，发送结果
7. **客户端更新** → 手牌减少，组合区域显示中发白
8. **恢复流程** → 庄家可以正常出牌

按照这个流程逐步检查，应该能找到问题所在。
